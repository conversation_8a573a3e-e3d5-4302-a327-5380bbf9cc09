import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import apiService from '../../services/apiService';
import { useNotifications } from '../../hooks/useNotifications';
import LoadingSpinner from '../UI/LoadingSpinner';
import ErrorMessage from '../UI/ErrorMessage';

/**
 * AssessmentStatus Component - Uses Observer Pattern (No Polling)
 *
 * Stage Transitions:
 * Stage 1 (Transforming) → Stage 2 (Analyzing) → Stage 3 (Preparing)
 *
 * Observers:
 * 1. Submit Observer: Detects successful API submission → Transitions to Stage 2 after 3s
 * 2. WebSocket Observer: Listens for analysis-complete notification → Transitions to Stage 3
 *
 * No polling is used - purely event-driven architecture
 */
const AssessmentStatus = () => {
  const { jobId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const [status, setStatus] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentStage, setCurrentStage] = useState('transforming'); // transforming, analyzing, preparing
  const websocketTimeoutRef = useRef(null);
  const stageTransitionTimeoutRef = useRef(null);
  const hasTransitionedToAnalyzing = useRef(false);

  // Observer Pattern: Setup WebSocket notifications as observers
  const { isConnected, isAuthenticated, notifications, clearNotification } = useNotifications({
    onAnalysisComplete: (data) => {
      console.log('🔔 Observer: Analysis complete notification received:', data);
      console.log('🔍 Observer: Current jobId:', jobId);
      console.log('🔍 Observer: Received jobId:', data.jobId);

      if (data.jobId === jobId) {
        console.log('✅ Observer: JobId matches - transitioning to Stage 3 (Preparing)');

        // Clear websocket timeout since we got the notification
        if (websocketTimeoutRef.current) {
          console.log('🛑 Observer: Clearing websocket timeout');
          clearTimeout(websocketTimeoutRef.current);
          websocketTimeoutRef.current = null;
        }

        // Observer: Transition to Stage 3 (Preparing)
        console.log('🎬 Stage 3: Transitioning to preparing stage');
        transitionToStage('preparing', 500);

        // Show "Preparing your personalized report" for 2 seconds before redirecting
        setTimeout(() => {
          console.log('🚀 Observer: Navigating to results page:', `/results/${data.resultId}`);
          navigate(`/results/${data.resultId}`);
        }, 2000);
      } else {
        console.log('❌ Observer: JobId mismatch, ignoring notification');
        console.log('❌ Expected:', jobId, 'Received:', data.jobId);
      }
    },
    onAnalysisFailed: (data) => {
      console.log('🔔 Observer: Analysis failed notification received:', data);
      if (data.jobId === jobId) {
        console.log('❌ Observer: Analysis failed for our job');
        setError(data.message || 'Analysis failed');
      }
    }
  });

  // Observer: Log websocket connection status changes
  useEffect(() => {
    console.log('🌐 Observer: WebSocket connection status changed:', {
      isConnected,
      isAuthenticated,
      jobId,
      observerActive: isConnected && isAuthenticated
    });
  }, [isConnected, isAuthenticated, jobId]);

  // Initial status check (only called once)
  const checkInitialStatus = async () => {
    try {
      console.log('📡 Initial status check for jobId:', jobId);
      const response = await apiService.getAssessmentStatus(jobId);
      console.log('📊 Initial status response:', response);

      if (response.success) {
        const statusData = response.data;
        console.log('📋 Initial status data:', statusData);
        setStatus(statusData);

        // If already completed when we first check, go straight to preparing
        if (statusData.status === 'completed') {
          console.log('🟢 Already completed on initial check -> Going to preparing stage');
          transitionToStage('preparing', 1000);

          // Set fallback timeout for redirect
          websocketTimeoutRef.current = setTimeout(() => {
            console.log('⏰ Websocket timeout - redirecting with resultId from status API');
            console.log('🚀 Using resultId from status:', statusData.resultId);
            navigate(`/results/${statusData.resultId || jobId}`);
          }, 5000);
        } else if (statusData.status === 'failed') {
          console.log('🔴 Failed on initial check');
          setError('Analysis failed. Please try again.');
        }
        // For 'queued' or 'processing', we rely on observer pattern
      }
    } catch (err) {
      console.error('❌ Error checking initial status:', err);
      setError(err.response?.data?.message || 'Failed to check status');
    } finally {
      setIsLoading(false);
    }
  };

  // Smooth stage transition function
  const transitionToStage = (newStage, delay = 0) => {
    if (stageTransitionTimeoutRef.current) {
      clearTimeout(stageTransitionTimeoutRef.current);
    }

    stageTransitionTimeoutRef.current = setTimeout(() => {
      console.log(`🎬 Transitioning to stage: ${newStage}`);
      setCurrentStage(newStage);
    }, delay);
  };

  useEffect(() => {
    if (!jobId) {
      navigate('/assessment');
      return;
    }

    console.log('🚀 AssessmentStatus mounted with jobId:', jobId);

    // Observer Pattern: Check if we just came from assessment submission
    const isFromSubmission = location.state?.fromSubmission;
    if (isFromSubmission) {
      console.log('📝 Observer: Coming from assessment submission');
      console.log('🎬 Stage 1: Starting in transforming stage');
      setCurrentStage('transforming');

      // Observer: Transition to analyzing after API submission is complete (3 seconds)
      console.log('⏰ Observer: Scheduling transition to analyzing stage in 3 seconds');
      transitionToStage('analyzing', 3000);
      hasTransitionedToAnalyzing.current = true;
    }

    // Only do initial status check (no polling)
    checkInitialStatus();

    console.log('🔍 Observer pattern active - no polling, waiting for WebSocket notifications');

    // Cleanup
    return () => {
      if (websocketTimeoutRef.current) {
        console.log('🧹 Cleaning up websocket timeout');
        clearTimeout(websocketTimeoutRef.current);
        websocketTimeoutRef.current = null;
      }
      if (stageTransitionTimeoutRef.current) {
        console.log('🧹 Cleaning up stage transition timeout');
        clearTimeout(stageTransitionTimeoutRef.current);
        stageTransitionTimeoutRef.current = null;
      }
    };
  }, [jobId, navigate, location.state]);

  const getStageInfo = (stage) => {
    switch (stage) {
      case 'transforming':
        return {
          title: 'Transforming Your Data',
          description: 'Securely processing and organizing your assessment responses with enterprise-grade encryption and data handling protocols.',
          color: 'text-amber-700',
          bgColor: 'from-amber-50 to-orange-100',
          ringColor: 'ring-amber-200/50',
          glowColor: 'shadow-amber-200/40',
          iconColor: 'text-amber-600'
        };
      case 'analyzing':
        return {
          title: 'AI-Powered Analysis',
          description: 'Advanced neural networks and machine learning algorithms are analyzing your responses to generate comprehensive career insights.',
          color: 'text-blue-700',
          bgColor: 'from-blue-50 to-indigo-100',
          ringColor: 'ring-blue-200/50',
          glowColor: 'shadow-blue-200/40',
          iconColor: 'text-blue-600'
        };
      case 'preparing':
        return {
          title: 'Crafting Your Report',
          description: 'Generating your personalized talent profile with actionable career recommendations and detailed performance insights.',
          color: 'text-emerald-700',
          bgColor: 'from-emerald-50 to-green-100',
          ringColor: 'ring-emerald-200/50',
          glowColor: 'shadow-emerald-200/40',
          iconColor: 'text-emerald-600'
        };
      default:
        return {
          title: 'Processing Assessment',
          description: 'Your assessment is being processed...',
          color: 'text-slate-700',
          bgColor: 'from-slate-50 to-gray-100',
          ringColor: 'ring-slate-200/50',
          glowColor: 'shadow-slate-200/40',
          iconColor: 'text-slate-600'
        };
    }
  };

  const getStageIcon = (stage) => {
    const stageInfo = getStageInfo(stage);

    switch (stage) {
      case 'transforming':
        return (
          <div className="relative group">
            <div className={`absolute inset-0 bg-gradient-to-r ${stageInfo.bgColor} rounded-2xl opacity-75 group-hover:opacity-100 transition-all duration-300 blur-sm`}></div>
            <div className={`relative h-20 w-20 bg-gradient-to-br ${stageInfo.bgColor} rounded-2xl shadow-lg ${stageInfo.glowColor} shadow-xl ring-1 ${stageInfo.ringColor} flex items-center justify-center backdrop-blur-sm`}>
              <div className="absolute inset-0 rounded-2xl bg-white/20 animate-pulse"></div>
              <svg className={`h-10 w-10 ${stageInfo.iconColor} relative z-10`} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 12a7.5 7.5 0 0015 0m-15 0a7.5 7.5 0 1115 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077l1.41-.513m14.095-5.13l1.41-.513M5.106 17.785l1.15-.964m11.49-9.642l1.149-.964M7.501 19.795l.75-1.3m7.5-12.99l.75-1.3m-6.063 16.658l.26-1.477m2.605-14.772l.26-1.477m0 17.726l-.26-1.477M10.698 4.614l-.26-1.477" />
              </svg>
            </div>
          </div>
        );
      case 'analyzing':
        return (
          <div className="relative group">
            <div className={`absolute inset-0 bg-gradient-to-r ${stageInfo.bgColor} rounded-2xl opacity-75 group-hover:opacity-100 transition-all duration-300 blur-sm`}></div>
            <div className={`relative h-20 w-20 bg-gradient-to-br ${stageInfo.bgColor} rounded-2xl shadow-lg ${stageInfo.glowColor} shadow-xl ring-1 ${stageInfo.ringColor} flex items-center justify-center backdrop-blur-sm`}>
              <div className="absolute inset-2 rounded-xl border-4 border-blue-300/30 border-t-blue-500 animate-spin"></div>
              <svg className={`h-10 w-10 ${stageInfo.iconColor} relative z-10`} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z" />
              </svg>
            </div>
          </div>
        );
      case 'preparing':
        return (
          <div className="relative group">
            <div className={`absolute inset-0 bg-gradient-to-r ${stageInfo.bgColor} rounded-2xl opacity-75 group-hover:opacity-100 transition-all duration-300 blur-sm`}></div>
            <div className={`relative h-20 w-20 bg-gradient-to-br ${stageInfo.bgColor} rounded-2xl shadow-lg ${stageInfo.glowColor} shadow-xl ring-1 ${stageInfo.ringColor} flex items-center justify-center backdrop-blur-sm`}>
              <div className="absolute inset-0 rounded-2xl bg-white/30 animate-pulse"></div>
              <svg className={`h-10 w-10 ${stageInfo.iconColor} relative z-10`} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        );
      default:
        return (
          <div className="h-20 w-20 bg-gradient-to-br from-slate-200 to-gray-300 rounded-2xl shadow-lg ring-1 ring-slate-200/50"></div>
        );
    }
  };

  if (isLoading && !status) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 flex items-center justify-center p-4">
        <div className="text-center max-w-md">
          <div className="relative mb-8 flex justify-center">
            {getStageIcon('transforming')}
          </div>
          <div className="space-y-6">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold text-slate-800 tracking-tight">Initializing Process</h2>
              <p className="text-slate-600 text-lg leading-relaxed">Setting up your assessment environment...</p>
            </div>
            <div className="flex justify-center">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-amber-500 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-amber-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-amber-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 py-8 px-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-slate-700 to-slate-800 rounded-2xl shadow-lg shadow-slate-800/20 mb-6 ring-1 ring-slate-200/50">
            <svg className="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
            </svg>
          </div>
          <h1 className="text-5xl font-bold text-slate-800 mb-4 tracking-tight">
            Assessment Processing
          </h1>
          <p className="text-xl text-slate-600 max-w-2xl mx-auto leading-relaxed">
            Your comprehensive talent assessment is being processed using advanced AI technology
          </p>
        </div>

        {/* Main Content */}
        <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl shadow-slate-200/50 border border-slate-200/50 overflow-hidden">
          {error ? (
            <div className="p-10">
              <ErrorMessage
                title="Assessment Error"
                message={error}
                onRetry={() => navigate('/assessment')}
                retryText="Start New Assessment"
              />
            </div>
          ) : status ? (
            <div className="p-10">
              {/* Progress Steps */}
              <div className="mb-12">
                <div className="flex justify-center items-center space-x-8 mb-10">
                  {/* Step 1: Transforming */}
                  <div className="flex items-center space-x-3">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold ring-2 transition-all duration-300 ${
                      currentStage === 'transforming' ? 'bg-amber-500 text-white ring-amber-200 shadow-lg shadow-amber-200/40' :
                      ['analyzing', 'preparing'].includes(currentStage) ? 'bg-emerald-500 text-white ring-emerald-200 shadow-lg shadow-emerald-200/40' :
                      'bg-slate-200 text-slate-600 ring-slate-200'
                    }`}>
                      {['analyzing', 'preparing'].includes(currentStage) ? (
                        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                          <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                        </svg>
                      ) : (
                        '1'
                      )}
                    </div>
                    <div className="hidden sm:block">
                      <span className={`text-sm font-medium transition-colors duration-300 ${
                        currentStage === 'transforming' ? 'text-amber-700' :
                        ['analyzing', 'preparing'].includes(currentStage) ? 'text-emerald-700' :
                        'text-slate-500'
                      }`}>
                        Data Processing
                      </span>
                    </div>
                  </div>

                  {/* Connector */}
                  <div className={`h-px w-12 transition-colors duration-300 ${
                    ['analyzing', 'preparing'].includes(currentStage) ? 'bg-emerald-400' : 'bg-slate-300'
                  }`}></div>

                  {/* Step 2: Analyzing */}
                  <div className="flex items-center space-x-3">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold ring-2 transition-all duration-300 ${
                      currentStage === 'analyzing' ? 'bg-blue-500 text-white ring-blue-200 shadow-lg shadow-blue-200/40' :
                      currentStage === 'preparing' ? 'bg-emerald-500 text-white ring-emerald-200 shadow-lg shadow-emerald-200/40' :
                      'bg-slate-200 text-slate-600 ring-slate-200'
                    }`}>
                      {currentStage === 'preparing' ? (
                        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                          <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                        </svg>
                      ) : (
                        '2'
                      )}
                    </div>
                    <div className="hidden sm:block">
                      <span className={`text-sm font-medium transition-colors duration-300 ${
                        currentStage === 'analyzing' ? 'text-blue-700' :
                        currentStage === 'preparing' ? 'text-emerald-700' :
                        'text-slate-500'
                      }`}>
                        AI Analysis
                      </span>
                    </div>
                  </div>

                  {/* Connector */}
                  <div className={`h-px w-12 transition-colors duration-300 ${
                    currentStage === 'preparing' ? 'bg-emerald-400' : 'bg-slate-300'
                  }`}></div>

                  {/* Step 3: Preparing */}
                  <div className="flex items-center space-x-3">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold ring-2 transition-all duration-300 ${
                      currentStage === 'preparing' ? 'bg-emerald-500 text-white ring-emerald-200 shadow-lg shadow-emerald-200/40' :
                      'bg-slate-200 text-slate-600 ring-slate-200'
                    }`}>
                      3
                    </div>
                    <div className="hidden sm:block">
                      <span className={`text-sm font-medium transition-colors duration-300 ${
                        currentStage === 'preparing' ? 'text-emerald-700' : 'text-slate-500'
                      }`}>
                        Report Generation
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Current Stage Display */}
              <div className="text-center mb-12">
                <div className="flex justify-center mb-8">
                  {getStageIcon(currentStage)}
                </div>

                <div className="space-y-4">
                  <h2 className={`text-4xl font-bold ${getStageInfo(currentStage).color} tracking-tight`}>
                    {getStageInfo(currentStage).title}
                  </h2>
                  <p className="text-slate-600 text-lg max-w-2xl mx-auto leading-relaxed">
                    {getStageInfo(currentStage).description}
                  </p>
                </div>
              </div>

    

              {/* Time Estimation */}
              {status.estimatedTimeRemaining && (
                <div className="bg-gradient-to-r from-slate-50 to-slate-100 rounded-2xl p-6 mb-10 border border-slate-200/50 shadow-sm">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0 w-12 h-12 bg-slate-100 rounded-xl flex items-center justify-center ring-1 ring-slate-200/50">
                      <svg className="w-6 h-6 text-slate-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm font-semibold text-slate-700">Estimated Time Remaining</p>
                      <p className="text-sm text-slate-600">{status.estimatedTimeRemaining}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="p-10">
              <LoadingSpinner text="Loading Assessment Status..." />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AssessmentStatus;
