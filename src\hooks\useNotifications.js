import { useEffect, useState } from 'react';
import { useAuth } from '../context/AuthContext';
import notificationService from '../services/notificationService';
import { API_CONFIG } from '../config/api';

export const useNotifications = (options = {}) => {
  const { token, isAuthenticated } = useAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [isNotificationAuthenticated, setIsNotificationAuthenticated] = useState(false);
  const [notifications, setNotifications] = useState([]);

  useEffect(() => {
    if (!isAuthenticated || !token) {
      notificationService.disconnect();
      setIsConnected(false);
      setIsNotificationAuthenticated(false);
      return;
    }

    // Setup notification service
    notificationService
      .onConnect(() => {
        setIsConnected(true);
      })
      .onDisconnect(() => {
        setIsConnected(false);
        setIsNotificationAuthenticated(false);
      })
      .onAuthenticated(() => {
        setIsNotificationAuthenticated(true);
      })
      .onAuthError((error) => {
        console.error('Notification auth error:', error);
        setIsNotificationAuthenticated(false);
        // Handle token refresh or redirect to login if needed
        if (options.onAuthError) {
          options.onAuthError(error);
        }
      })
      .onAnalysisComplete((data) => {
        setNotifications(prev => [...prev, {
          id: Date.now(),
          type: 'success',
          title: 'Analysis Complete',
          message: data.message || 'Your analysis is ready!',
          data: data,
          timestamp: new Date()
        }]);

        if (options.onAnalysisComplete) {
          options.onAnalysisComplete(data);
        }
      })
      .onAnalysisFailed((data) => {
        setNotifications(prev => [...prev, {
          id: Date.now(),
          type: 'error',
          title: 'Analysis Failed',
          message: data.message || 'Analysis failed. Please try again.',
          data: data,
          timestamp: new Date()
        }]);

        if (options.onAnalysisFailed) {
          options.onAnalysisFailed(data);
        }
      })
      .connect(token, {
        url: API_CONFIG.NOTIFICATION_URL,
        ...options
      });

    // Cleanup on unmount
    return () => {
      notificationService.disconnect();
    };
  }, [isAuthenticated, token]);

  const clearNotification = (notificationId) => {
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
  };

  const clearAllNotifications = () => {
    setNotifications([]);
  };

  return {
    isConnected,
    isAuthenticated: isNotificationAuthenticated,
    notifications,
    clearNotification,
    clearAllNotifications,
    connectionStatus: notificationService.getConnectionStatus()
  };
};
